package com.crrc.siom.ui.workorder.pages

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.common.bean.response.TempWorkOrderParamResponse
import com.crrc.siom.ui.workorder.viewmodel.CreateTempWorkOrderViewModel
import androidx.compose.ui.tooling.preview.Preview
import com.crrc.common.utils.ToastUtil

/**
 * 前端处理选择逻辑之前赋值isselect.每次重选还得把之前选的再清空重选
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateTempWorkOrderPage(
    onBackClick: () -> Unit,
    viewModel: CreateTempWorkOrderViewModel = viewModel(),
) {
    val optionParam by viewModel.optionParam.collectAsState()
    CreateTempWorkOrderPageContent(
        onBackClick = onBackClick,
        optionParam = optionParam,
        viewModel = viewModel
    )
}
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateTempWorkOrderPageContent(
    onBackClick: () -> Unit,
    optionParam: TempWorkOrderParamResponse?,
    viewModel: CreateTempWorkOrderViewModel,
) {
    val scrollState = rememberScrollState()
    val typeOptions = optionParam?.type ?: emptyList()
    val lineOptions = optionParam?.line ?: emptyList()
    val stationOptions = optionParam?.station ?: emptyList()
    val groupOptions = optionParam?.group ?: emptyList()
    val personOptions = groupOptions.flatMap { it.members ?: emptyList() }
    val priorityOptions = optionParam?.priority ?: emptyList()
    var description by remember { mutableStateOf(optionParam?.description ?: "") }
    var instructions by remember { mutableStateOf(optionParam?.maintenanceGuide ?: "") }

    // 可变状态列表，确保多选触发 Compose 重组
    val standbyList = remember(optionParam?.standby) {
        mutableStateListOf<TempWorkOrderParamResponse.AmountItem>().apply {
            optionParam?.standby?.let { addAll(it) }
        }
    }
    val toolList = remember(optionParam?.tool) {
        mutableStateListOf<TempWorkOrderParamResponse.AmountItem>().apply {
            optionParam?.tool?.let { addAll(it) }
        }
    }

    Column(modifier = Modifier.fillMaxSize()) {
        TopAppBar(
            title = { Text("新建工单") },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            }
        )
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp)
        ) {
            FormRowSingleSelect("工单类型", typeOptions) {
                typeOptions.forEach { it.selected = false }
                it.selected = true
            }
            FormRowSingleSelect("所属线路", lineOptions) {
                lineOptions.forEach { it.selected = false }
                it.selected = true
            }
            FormRowSingleSelect("所属车站", stationOptions) {
                stationOptions.forEach { it.selected = false }
                it.selected = true
            }
            FormRowGroupSingleSelect("所属班组", groupOptions) {
                groupOptions.forEach { it.isSelected = false }
                it.isSelected = true
            }
            FormManagerText("负责人", groupOptions.find { it.isSelected }?.manager ?: "")
            FormRowSingleSelect("维修人员", personOptions) {
                personOptions.forEach { it.selected = false }
                it.selected = true
            }
            FormRowSingleSelect("优先级", priorityOptions) {
                priorityOptions.forEach { it.selected = false }
                it.selected = true
            }

            // 备品备件（多选）
            Column(modifier = Modifier.fillMaxWidth()) {
                Text(
                    text = "备品备件",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                LazyVerticalGrid(
                    columns = GridCells.Fixed(3),
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(MaterialTheme.colorScheme.surfaceVariant, RoundedCornerShape(8.dp))
                        .padding(8.dp)
                        .heightIn(max = 200.dp), // 限制最大高度，避免占用过多空间
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(standbyList) { item ->
                        val index = standbyList.indexOf(item)
                        PartsChip(
                            text = item.name,
                            isSelected = item.selected,
                            onClick = {
                                standbyList[index] = TempWorkOrderParamResponse.AmountItem().apply {
                                    id = item.id
                                    name = item.name
                                    amount = item.amount
                                    selected = !item.selected
                                }
                            }
                        )
                    }
                }
            }

            // 工器具（多选）
            Column(modifier = Modifier.fillMaxWidth()) {
                Text(
                    text = "工器具",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                LazyVerticalGrid(
                    columns = GridCells.Fixed(3),
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(MaterialTheme.colorScheme.surfaceVariant, RoundedCornerShape(8.dp))
                        .padding(8.dp)
                        .heightIn(max = 200.dp), // 限制最大高度，避免占用过多空间
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(toolList) { item ->
                        val index = toolList.indexOf(item)
                        PartsChip(
                            text = item.name,
                            isSelected = item.selected,
                            onClick = {
                                toolList[index] = TempWorkOrderParamResponse.AmountItem().apply {
                                    id = item.id
                                    name = item.name
                                    amount = item.amount
                                    selected = !item.selected
                                }
                            }
                        )
                    }
                }
            }

            // 工单描述
            Column {
                Text("工单描述", style = MaterialTheme.typography.bodyMedium, modifier = Modifier.padding(bottom = 8.dp))
                OutlinedTextField(
                    value = description,
                    onValueChange = {
                        description = it
                        optionParam?.description = it
                    },
                    modifier = Modifier.fillMaxWidth().height(120.dp),
                    placeholder = { Text("请输入工单描述") }
                )
            }

            // 维修指导
            Column {
                Text("维修指导", style = MaterialTheme.typography.bodyMedium, modifier = Modifier.padding(bottom = 8.dp))
                OutlinedTextField(
                    value = instructions,
                    onValueChange = {
                        instructions = it
                        optionParam?.maintenanceGuide = it
                    },
                    modifier = Modifier.fillMaxWidth().height(120.dp),
                    placeholder = { Text("请输入维修指导") }
                )
            }

            // 提交按钮（更新多选状态再提交）
            Button(
                onClick = {
                    optionParam?.standby = standbyList.toList()
                    optionParam?.tool = toolList.toList()
                    viewModel.submitTempWorkOrderParam(
                        onSuccess = {
                            ToastUtil.show("提交成功")
                            onBackClick()
                        },
                        onError = {
                            ToastUtil.show("提交失败")
                        }
                    )
                },
                modifier = Modifier.fillMaxWidth().padding(vertical = 16.dp)
            ) {
                Text("提交")
            }
        }
    }
}

@Composable
private fun FormRowSingleSelect(
    label: String,
    options: List<TempWorkOrderParamResponse.Item>,
    onSelect: (TempWorkOrderParamResponse.Item) -> Unit
) {
    val selected = options.find { it.selected }
    var expanded by remember { mutableStateOf(false) }
    val textFieldWidth = remember { mutableStateOf(0) }
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.width(80.dp)
        )
        Box(modifier = Modifier.fillMaxWidth()) {
            OutlinedTextField(
                value = selected?.name ?: "",
                onValueChange = {},
                readOnly = true,
                placeholder = { Text("请选择$label") },
                trailingIcon = {
                    Icon(
                        Icons.Default.KeyboardArrowDown,
                        contentDescription = null,
                        modifier = Modifier.clickable { expanded = true }
                    )
                },
                modifier = Modifier
                    .onGloballyPositioned { coordinates ->
                        textFieldWidth.value = coordinates.size.width
                    }
                    .fillMaxWidth()
                    .clickable { expanded = true }
            )
            DropdownMenu(
                expanded = expanded,
                modifier = Modifier.width(with(LocalDensity.current) { textFieldWidth.value.toDp() }),
                onDismissRequest = { expanded = false }
            ) {
                options.forEach { item ->
                    DropdownMenuItem(
                        text = { Text(item.name) },
                        onClick = {
                            onSelect(item)
                            expanded = false
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun FormManagerText(label: String, managerName: String) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.width(80.dp)
        )
        OutlinedTextField(
            value = managerName,
            onValueChange = {},
            readOnly = true,
        )

    }
}

@Composable
private fun FormRowGroupSingleSelect(
    label: String,
    options: List<TempWorkOrderParamResponse.Group>,
    onSelect: (TempWorkOrderParamResponse.Group) -> Unit
) {
    val selected = options.find { it.isSelected }
    var expanded by remember { mutableStateOf(false) }
    val textFieldWidth = remember { mutableStateOf(0) }

    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.width(80.dp)
        )
        Box(modifier = Modifier.fillMaxWidth()) {
            OutlinedTextField(
                value = selected?.name ?: "",
                onValueChange = {},
                readOnly = true,
                placeholder = { Text("请选择$label") },
                trailingIcon = {
                    Icon(
                        Icons.Default.KeyboardArrowDown,
                        contentDescription = null,
                        modifier = Modifier.clickable { expanded = true }
                    )
                },
                modifier = Modifier
                    .onGloballyPositioned { coordinates ->
                        textFieldWidth.value = coordinates.size.width
                    }
                    .fillMaxWidth()
                    .clickable { expanded = true }
            )
            DropdownMenu(
                expanded = expanded,
                modifier = Modifier.width(with(LocalDensity.current) { textFieldWidth.value.toDp() }),
                onDismissRequest = { expanded = false }
            ) {
                options.forEach { group ->
                    DropdownMenuItem(
                        text = { Text(group.name) },
                        onClick = {
                            onSelect(group)
                            expanded = false
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun PartsChip(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier.height(32.dp),
        shape = RoundedCornerShape(16.dp),
        color = if (isSelected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.surfaceVariant,
        onClick = onClick
    ) {
        Box(
            modifier = Modifier.padding(horizontal = 12.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = text,
                color = if (isSelected) MaterialTheme.colorScheme.onPrimary else MaterialTheme.colorScheme.onSurfaceVariant,
                style = MaterialTheme.typography.bodySmall,
                textAlign = TextAlign.Center
            )
        }
    }
}
